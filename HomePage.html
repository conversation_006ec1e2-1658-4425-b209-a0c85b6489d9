<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Lohia Corp Order Management System - Streamlined parts ordering and tracking">
    <meta name="theme-color" content="#EAB308">
    <title>Order Management System - Lohia Corp</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.tailwindcss.com">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                        'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Enhanced icon styling */
        .lucide {
            width: 1.25rem;
            height: 1.25rem;
            stroke-width: 2px;
            flex-shrink: 0;
            transition: all 0.2s ease-in-out;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; border-radius: 4px; }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
            transition: background 0.2s ease;
        }
        ::-webkit-scrollbar-thumb:hover { background: #94a3b8; }

        /* Modal animations */
        #ai-modal-backdrop {
            transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(4px);
        }
        #ai-modal-panel {
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        /* Loading spinner */
        .spinner {
            animation: spin 1s linear infinite;
            filter: drop-shadow(0 0 6px rgba(234, 179, 8, 0.3));
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Sidebar and main content transitions */
        #sidebar, #main-content {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Mobile overlay */
        .mobile-overlay {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            transition: opacity 0.3s ease-in-out;
        }

        /* Enhanced focus states */
        .focus-ring:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.3);
        }

        /* Button hover effects */
        .btn-primary {
            background: #EAB308;
            transition: all 0.2s ease-in-out;
        }
        .btn-primary:hover {
            background: #D97706;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(234, 179, 8, 0.3);
        }

        /* Card hover effects */
        .card-hover {
            transition: all 0.3s ease-in-out;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Custom animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }

        @keyframes bounceSubtle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-4px); }
        }

        /* Improved form styling */
        .form-input {
            transition: all 0.2s ease-in-out;
            border: 2px solid transparent;
        }
        .form-input:focus {
            border-color: #EAB308;
            box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }

        /* Toast notifications */
        .toast {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .toast.show {
            transform: translateX(0);
        }

        /* Floating Action Button Styles */
        #floating-actions {
            z-index: 1000;
        }

        #floating-actions button {
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        #floating-actions button:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }

        /* Task completion animation */
        .task-item.completed {
            transition: all 0.3s ease;
            opacity: 0.6;
        }

        /* Quick action button hover effects */
        .quick-action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-inter">

    <!-- Mobile Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden mobile-overlay"></div>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <div class="flex h-screen bg-white">
        <!-- Sidebar Navigation -->
        <aside id="sidebar" class="w-20 lg:w-20 bg-gray-800 text-gray-200 flex flex-col fixed inset-y-0 left-0 z-50 lg:z-30 overflow-hidden shadow-2xl transform -translate-x-full lg:translate-x-0">
            <!-- Mobile Close Button -->
            <button id="mobile-close-btn" class="absolute top-4 right-4 text-gray-400 hover:text-white lg:hidden focus-ring rounded-lg p-2" aria-label="Close navigation">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>

            <!-- Logo -->
            <div class="h-20 flex items-center justify-center px-4 border-b border-gray-700 shrink-0">
                <div id="logo-container" class="bg-white p-2 rounded-xl shadow-lg w-12 h-12 flex items-center justify-center transition-all duration-300 hover:shadow-xl">
                    <img src="Images/LCLLogo.png" alt="LohiaCorp Logo" class="h-8 w-auto transition-all duration-300" loading="lazy">
                </div>
            </div>

            <!-- Nav Links -->
            <nav class="flex-1 overflow-y-auto overflow-x-hidden" role="navigation" aria-label="Main navigation">
                <!-- Commonly Used Section -->
                <div class="p-4">
                    <h4 class="nav-text text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 opacity-0">Commonly Used</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 bg-yellow-500 text-gray-900 font-semibold rounded-xl shadow-lg transition-all duration-200" role="menuitem" aria-label="Home" aria-current="page">
                                <i data-lucide="home" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0">Home</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="bg-yellow-600 text-yellow-100 text-xs px-2 py-1 rounded-full">Most Used</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Part Finder">
                                <i data-lucide="search" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Part Finder</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="bg-blue-600 text-blue-100 text-xs px-2 py-1 rounded-full">Daily</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring relative" role="menuitem" aria-label="Shopping Cart - 3 items">
                                <div class="relative">
                                    <i data-lucide="shopping-cart" class="w-5 h-5 flex-shrink-0"></i>
                                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold">3</span>
                                </div>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Cart</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="bg-green-600 text-green-100 text-xs px-2 py-1 rounded-full">Active</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Track Orders">
                                <i data-lucide="truck" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Track Orders</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="bg-orange-600 text-orange-100 text-xs px-2 py-1 rounded-full">Frequent</span>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Recently Used Section -->
                <div class="p-4 border-t border-gray-600">
                    <h4 class="nav-text text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 opacity-0">Recently Used</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Part Ordering">
                                <i data-lucide="package-plus" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Part Ordering</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="text-xs text-gray-400">2h ago</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Created Orders">
                                <i data-lucide="file-check-2" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Created Orders</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="text-xs text-gray-400">1d ago</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Dashboard">
                                <i data-lucide="layout-dashboard" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Dashboard</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="text-xs text-gray-400">2d ago</span>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Other Features Section -->
                <div class="p-4 border-t border-gray-600">
                    <h4 class="nav-text text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 opacity-0">Other Features</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Closed Orders">
                                <i data-lucide="archive" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Closed Orders</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="About Us">
                                <i data-lucide="info" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">About Us</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer / User Info -->
            <div class="p-4 border-t border-gray-700 shrink-0">
                <a href="#" class="flex items-center gap-3 group hover:bg-gray-700 rounded-xl p-2 transition-all duration-200 focus-ring" aria-label="User profile and logout">
                    <img src="https://placehold.co/40x40/E2E8F0/4A5568?text=K" alt="User Avatar" class="w-10 h-10 rounded-full flex-shrink-0 ring-2 ring-gray-600 group-hover:ring-yellow-500 transition-all duration-200" loading="lazy">
                    <div class="flex-1 nav-text opacity-0 transition-opacity duration-200">
                        <p class="font-semibold text-white text-sm whitespace-nowrap">Kanpur Plastipack</p>
                        <p class="text-xs text-gray-400 whitespace-nowrap">ID: 10340</p>
                    </div>
                    <i data-lucide="log-out" class="nav-text opacity-0 transition-all duration-200 group-hover:text-red-400"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main id="main-content" class="flex-1 flex flex-col h-screen lg:ml-20 transition-all duration-300">
            <!-- Header -->
            <header class="h-16 lg:h-20 bg-white border-b border-gray-200 flex flex-col justify-center px-4 sm:px-6 lg:px-8 shrink-0 shadow-sm">
                <!-- Top Header Row -->
                <div class="flex items-center justify-between">
                    <!-- Mobile Menu Button & Title -->
                    <div class="flex items-center gap-4">
                        <button id="mobile-menu-btn" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 focus-ring transition-colors" aria-label="Open navigation menu">
                            <i data-lucide="menu" class="w-6 h-6 text-gray-600"></i>
                            <span class="sr-only">Menu</span>
                        </button>
                        <div>
                            <h1 class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 tracking-tight">Order Management System</h1>
                            <p class="text-xs sm:text-sm text-gray-500 hidden sm:block"></p>
                        </div>
                    </div>

                    <!-- Header Actions -->
                    <div class="flex items-center gap-2 sm:gap-4">
                        <!-- Notifications -->
                        <button class="relative p-2 rounded-lg hover:bg-gray-100 focus-ring transition-colors hidden sm:flex items-center gap-2" aria-label="View notifications">
                            <i data-lucide="bell" class="w-5 h-5 text-gray-600"></i>
                            <span class="hidden lg:inline text-sm text-gray-600">Notifications</span>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">2</span>
                        </button>

                        <!-- Activity Log -->
                        <button class="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 px-3 py-2 rounded-lg transition-all duration-200 focus-ring" onclick="showActivityLog()" aria-label="View activity log">
                            <i data-lucide="activity" class="w-5 h-5" aria-hidden="true"></i>
                            <span class="hidden lg:inline">Activity Log</span>
                        </button>

                        <!-- Parts Policy -->
                        <button class="flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-3 py-2 rounded-lg transition-all duration-200 focus-ring">
                            <i data-lucide="shield-check" class="w-5 h-5"></i>
                            <span class="hidden sm:inline">Parts Policy</span>
                        </button>

                        <div class="w-px h-6 bg-gray-300 hidden md:block"></div>

                        <!-- User Info -->
                        <div class="hidden md:flex items-center gap-3">
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-700">Welcome, Kanpur Plastipack Limited</div>
                                <div class="text-xs text-gray-500"><EMAIL></div>
                            </div>
                            <button class="w-10 h-10 rounded-full bg-yellow-500 text-white flex items-center justify-center focus-ring font-semibold" aria-label="User profile menu">
                                K
                            </button>
                        </div>

                        <!-- Mobile User Avatar -->
                        <button class="md:hidden w-8 h-8 rounded-full bg-yellow-500 text-white flex items-center justify-center focus-ring font-semibold" aria-label="User menu">
                            K
                        </button>
                    </div>
                </div>

                <!-- Breadcrumbs -->
                <nav class="flex items-center space-x-2 text-sm text-gray-500 mt-2" aria-label="Breadcrumb">
                    <a href="#" class="flex items-center gap-1 hover:text-gray-700 transition-colors">
                        <i data-lucide="home" class="w-4 h-4"></i>
                        <span>Home</span>
                    </a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span id="current-page" class="text-gray-900 font-medium">Parts Number Enquiry</span>
                </nav>
            </header>

            <!-- Page Content -->
            <div class="flex-1 p-3 sm:p-4 lg:p-6 overflow-y-auto bg-gray-50">
                <div class="w-full space-y-4">
                    <!-- Welcome Banner -->
                    <div class="bg-yellow-500 rounded-2xl p-6 text-white shadow-xl animate-fade-in">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                            <div>
                                <h2 class="text-xl sm:text-2xl font-bold mb-2">Welcome to Order Management</h2>
                                <p class="text-yellow-100 text-sm sm:text-base">Find parts, check prices, and manage your orders efficiently</p>
                            </div>
                            <div class="flex items-center gap-2 text-yellow-100">
                                <i data-lucide="clock" class="w-4 h-4"></i>
                                <span class="text-sm" id="current-time"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 animate-fade-in">
                        <div class="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow card-hover">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <i data-lucide="package" class="w-5 h-5 text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">1,247</p>
                                    <p class="text-xs text-gray-500">Available Parts</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow card-hover">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-green-100 rounded-lg">
                                    <i data-lucide="shopping-cart" class="w-5 h-5 text-green-600"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">23</p>
                                    <p class="text-xs text-gray-500">Active Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow card-hover">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-yellow-100 rounded-lg">
                                    <i data-lucide="truck" class="w-5 h-5 text-yellow-600"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">8</p>
                                    <p class="text-xs text-gray-500">In Transit</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow card-hover">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-purple-600"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">156</p>
                                    <p class="text-xs text-gray-500">Completed</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions & Today's Tasks Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <!-- Quick Actions -->
                        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-2">
                                    <i data-lucide="zap" class="w-5 h-5 text-yellow-500" aria-hidden="true"></i>
                                    Quick Actions
                                </h3>
                                <button class="text-sm text-blue-600 hover:text-blue-800 font-medium focus-ring rounded-lg px-2 py-1" onclick="customizeQuickActions()">Customize</button>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <button class="quick-action-btn p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors text-left border border-blue-200" onclick="quickAction('search')" data-action="search">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="search" class="w-4 h-4 text-blue-600" aria-hidden="true"></i>
                                        <span class="font-medium text-blue-900">Quick Search</span>
                                    </div>
                                    <p class="text-xs text-blue-700">Find parts instantly</p>
                                </button>
                                <button class="quick-action-btn p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors text-left border border-green-200" onclick="quickAction('newOrder')" data-action="newOrder">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="plus-circle" class="w-4 h-4 text-green-600" aria-hidden="true"></i>
                                        <span class="font-medium text-green-900">New Order</span>
                                    </div>
                                    <p class="text-xs text-green-700">Create new order</p>
                                </button>
                                <button class="quick-action-btn p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors text-left border border-orange-200" onclick="quickAction('trackOrder')" data-action="trackOrder">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="truck" class="w-4 h-4 text-orange-600" aria-hidden="true"></i>
                                        <span class="font-medium text-orange-900">Track Order</span>
                                    </div>
                                    <p class="text-xs text-orange-700">Check order status</p>
                                </button>
                                <button class="quick-action-btn p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors text-left border border-purple-200" onclick="quickAction('aiAssist')" data-action="aiAssist">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="brain" class="w-4 h-4 text-purple-600" aria-hidden="true"></i>
                                        <span class="font-medium text-purple-900">AI Assistant</span>
                                    </div>
                                    <p class="text-xs text-purple-700">Get smart help</p>
                                </button>
                            </div>
                        </div>

                        <!-- Today's Tasks -->
                        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-2">
                                    <i data-lucide="calendar-check" class="w-5 h-5 text-green-500" aria-hidden="true"></i>
                                    Today's Tasks
                                </h3>
                                <button class="text-sm text-blue-600 hover:text-blue-800 font-medium focus-ring rounded-lg px-2 py-1" onclick="viewAllTasks()">View All</button>
                            </div>
                            <div class="space-y-3" id="todays-tasks">
                                <div class="task-item flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                    <input type="checkbox" class="w-4 h-4 text-yellow-500 border-gray-300 rounded focus:ring-yellow-500" onchange="completeTask(this, 'review-order-156')">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">Review Order #ORD-2024-156</p>
                                        <p class="text-xs text-gray-600">Due: 2:00 PM</p>
                                    </div>
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">High</span>
                                </div>
                                <div class="task-item flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                    <input type="checkbox" class="w-4 h-4 text-blue-500 border-gray-300 rounded focus:ring-blue-500" onchange="completeTask(this, 'approve-parts-list')">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">Approve Parts List</p>
                                        <p class="text-xs text-gray-600">Due: 4:30 PM</p>
                                    </div>
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Medium</span>
                                </div>
                                <div class="task-item flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                                    <input type="checkbox" class="w-4 h-4 text-green-500 border-gray-300 rounded focus:ring-green-500" onchange="completeTask(this, 'update-inventory')">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">Update Inventory Status</p>
                                        <p class="text-xs text-gray-600">Due: End of day</p>
                                    </div>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Low</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Forms Section -->
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden card-hover">
                        <!-- Tab Navigation -->
                        <div class="border-b border-gray-200 bg-gray-50">
                            <nav class="flex overflow-x-auto" id="search-tabs" role="tablist">
                                <button data-tab="numberEnquiry" class="tab-button flex-shrink-0 text-sm sm:text-base font-semibold text-yellow-600 border-b-2 border-yellow-500 py-4 px-6 hover:bg-white transition-colors focus-ring" role="tab" aria-selected="true" aria-label="Parts Number Enquiry Tab">
                                    <span class="flex items-center gap-2">
                                        <i data-lucide="search" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                        <span class="whitespace-nowrap">Parts Number Enquiry</span>
                                    </span>
                                </button>
                                <button data-tab="priceEnquiry" class="tab-button flex-shrink-0 text-sm sm:text-base font-semibold text-gray-500 hover:text-gray-700 hover:bg-white py-4 px-6 transition-colors focus-ring" role="tab" aria-selected="false" aria-label="Price Enquiry Tab">
                                    <span class="flex items-center gap-2">
                                        <i data-lucide="dollar-sign" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                        <span class="whitespace-nowrap">Price Enquiry</span>
                                    </span>
                                </button>
                                <button data-tab="aiSearch" class="tab-button flex-shrink-0 text-sm sm:text-base font-semibold text-gray-500 hover:text-gray-700 hover:bg-white py-4 px-6 transition-colors focus-ring" role="tab" aria-selected="false" aria-label="AI-Powered Search Tab">
                                    <span class="flex items-center gap-2">
                                        <i data-lucide="sparkles" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                        <span class="hidden sm:inline whitespace-nowrap">AI-Powered Search</span>
                                        <span class="sm:hidden whitespace-nowrap">AI Search</span>
                                    </span>
                                </button>
                            </nav>
                        </div>

                        <!-- Tab Content -->
                        <div id="form-container" class="p-6">
                            <!-- Parts Number Enquiry Tab -->
                            <div id="numberEnquiry" class="tab-content space-y-8" role="tabpanel">
                                <div class="grid grid-cols-1 xl:grid-cols-2 gap-8">
                                    <!-- Catalog Search Form -->
                                    <form class="p-6 border-2 border-gray-100 rounded-2xl space-y-6 hover:border-yellow-200 transition-colors card-hover">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="p-2 bg-yellow-100 rounded-lg">
                                                <i data-lucide="book-open" class="w-5 h-5 text-yellow-600"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-800">Part Search by Catalog</h3>
                                        </div>
                                        <div>
                                            <label for="catalogNumber" class="block text-sm font-medium text-gray-700 mb-2">Catalog Number</label>
                                            <div class="relative">
                                                <input type="text" id="catalogNumber" placeholder="Enter catalog number..." class="form-input w-full pl-4 pr-12 py-3 border-2 border-gray-200 rounded-xl focus-ring transition-all" aria-describedby="catalog-help">
                                                <button type="button" id="catalog-search-btn" class="absolute inset-y-0 right-0 flex items-center px-4 bg-yellow-500 hover:bg-yellow-600 text-white rounded-r-xl transition-colors focus-ring">
                                                    <i data-lucide="search" class="w-5 h-5"></i>
                                                </button>
                                            </div>
                                            <p id="catalog-help" class="text-xs text-gray-500 mt-1">Enter the part catalog number to search</p>
                                        </div>
                                    </form>

                                    <!-- Serial Number Search Form -->
                                    <form class="p-6 border-2 border-gray-100 rounded-2xl space-y-6 hover:border-yellow-200 transition-colors card-hover">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="p-2 bg-blue-100 rounded-lg">
                                                <i data-lucide="settings" class="w-5 h-5 text-blue-600"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-800">Part Search by M/C Serial No and Model</h3>
                                        </div>
                                        <div class="space-y-4">
                                            <div>
                                                <label for="mfgYear" class="block text-sm font-medium text-gray-700 mb-2">Manufacturing Year</label>
                                                <select id="mfgYear" class="form-input w-full p-3 border-2 border-gray-200 rounded-xl focus-ring transition-all">
                                                    <option value="">--Select Year--</option>
                                                    <option value="2025">2025</option>
                                                    <option value="2024">2024</option>
                                                    <option value="2023">2023</option>
                                                    <option value="2022">2022</option>
                                                    <option value="2021">2021</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label for="modelDesc" class="block text-sm font-medium text-gray-700 mb-2">Model Description</label>
                                                <select id="modelDesc" class="form-input w-full p-3 border-2 border-gray-200 rounded-xl focus-ring transition-all">
                                                    <option value="">--Select Model--</option>
                                                    <option value="model-a">Model A - Standard Weaver</option>
                                                    <option value="model-b">Model B - Heavy Duty</option>
                                                    <option value="model-c">Model C - Compact Series</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label for="serialNumber" class="block text-sm font-medium text-gray-700 mb-2">Serial Number</label>
                                                <div class="relative">
                                                    <select id="serialNumber" class="form-input w-full p-3 pr-12 border-2 border-gray-200 rounded-xl focus-ring transition-all appearance-none">
                                                        <option value="">--Select Serial Number--</option>
                                                    </select>
                                                    <button type="button" id="serial-search-btn" class="absolute inset-y-0 right-0 flex items-center px-4 bg-blue-500 hover:bg-blue-600 text-white rounded-r-xl transition-colors focus-ring">
                                                        <i data-lucide="search" class="w-5 h-5"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Price Enquiry Tab -->
                            <div id="priceEnquiry" class="tab-content hidden" role="tabpanel">
                                <div class="max-w-2xl mx-auto">
                                    <form id="priceEnquiryForm" class="p-8 border-2 border-gray-100 rounded-2xl space-y-6 hover:border-yellow-200 transition-colors card-hover">
                                        <div class="flex items-center gap-3 mb-6">
                                            <div class="p-3 bg-green-100 rounded-xl">
                                                <i data-lucide="dollar-sign" class="w-6 h-6 text-green-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-xl font-semibold text-gray-800">Price Enquiry</h3>
                                                <p class="text-sm text-gray-600">Get instant pricing for any part number</p>
                                            </div>
                                        </div>

                                        <div>
                                            <label for="partNumber" class="block text-sm font-medium text-gray-700 mb-2">Part Number</label>
                                            <div class="relative">
                                                <input type="text" id="partNumber" placeholder="Enter Part Number (e.g., LP-2024-001)" class="form-input w-full pl-4 pr-12 py-4 border-2 border-gray-200 rounded-xl text-lg focus-ring transition-all" aria-describedby="part-help">
                                                <button type="button" class="absolute inset-y-0 right-0 flex items-center px-4 bg-gray-100 hover:bg-gray-200 rounded-r-xl transition-colors focus-ring">
                                                    <i data-lucide="search" class="w-5 h-5 text-gray-600"></i>
                                                </button>
                                            </div>
                                            <p id="part-help" class="text-xs text-gray-500 mt-2">Enter the exact part number to get pricing information</p>
                                        </div>

                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <button type="submit" class="btn-primary w-full py-4 rounded-xl font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-200 focus-ring flex items-center justify-center gap-2">
                                                <i data-lucide="dollar-sign" class="w-5 h-5"></i>
                                                Check Price
                                            </button>
                                            <button id="getAiAnalysis" type="button" class="w-full bg-blue-600 text-white font-semibold py-4 rounded-xl hover:bg-blue-700 focus-ring transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2" aria-label="Get AI analysis of part">
                                                <i data-lucide="brain" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                                <span>Get AI Analysis</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- AI Search Tab -->
                            <div id="aiSearch" class="tab-content hidden" role="tabpanel">
                                <div class="max-w-3xl mx-auto">
                                    <form id="aiSearchForm" class="p-8 border-2 border-gray-100 rounded-2xl space-y-6 hover:border-blue-200 transition-colors card-hover">
                                        <div class="flex items-center gap-3 mb-6">
                                            <div class="p-3 bg-purple-100 rounded-xl">
                                                <span class="text-2xl">✨</span>
                                            </div>
                                            <div>
                                                <h3 class="text-xl font-semibold text-gray-800">AI-Powered Part Search</h3>
                                                <p class="text-sm text-gray-600">Describe what you need and let AI find the right part</p>
                                            </div>
                                        </div>

                                        <div>
                                            <label for="partDescription" class="block text-sm font-medium text-gray-700 mb-2">Describe the part you need</label>
                                            <textarea id="partDescription" rows="5" placeholder="e.g., 'A reinforced drive belt for the main assembly of a 2023 Model X weaver that can handle high tension loads...'" class="form-input w-full p-4 border-2 border-gray-200 rounded-xl resize-none focus-ring transition-all" aria-describedby="description-help"></textarea>
                                            <p id="description-help" class="text-xs text-gray-500 mt-2">Be as specific as possible about the part's function, location, and requirements</p>
                                        </div>

                                        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                                            <h4 class="font-medium text-blue-900 mb-2">💡 Tips for better results:</h4>
                                            <ul class="text-sm text-blue-800 space-y-1">
                                                <li>• Include the machine model and year</li>
                                                <li>• Mention the part's function or location</li>
                                                <li>• Describe any specific requirements (size, material, etc.)</li>
                                                <li>• Include any part numbers you might know</li>
                                            </ul>
                                        </div>

                                        <button type="submit" class="w-full bg-purple-600 text-white font-semibold py-4 rounded-xl hover:bg-purple-700 focus-ring transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2" aria-label="Find part number using AI">
                                            <i data-lucide="sparkles" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                            <span>Find Part Number with AI</span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Results Grid -->
                    <div id="search-results-container" class="bg-white rounded-2xl shadow-xl overflow-hidden card-hover hidden">
                        <div class="bg-yellow-500 text-white p-4">
                            <h3 class="text-lg font-semibold">Search Results</h3>
                            <p class="text-sm text-yellow-100">Found <span id="results-count">0</span> matching parts</p>
                        </div>

                        <!-- Results Table -->
                        <div class="border border-gray-300">
                            <table class="w-full text-xs border-collapse table-fixed">
                                <thead class="bg-gray-100">
                                    <tr class="border-b-2 border-gray-300">
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-12">Select</th>
                                        <th class="text-left p-1 font-semibold text-gray-700 border-r border-gray-300 w-20">Part Number</th>
                                        <th class="text-left p-1 font-semibold text-gray-700 border-r border-gray-300 w-32">Part Description</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">HSN</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">MOQ</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">Lot Size</th>
                                        <th class="text-right p-1 font-semibold text-gray-700 border-r border-gray-300 w-24">Unit Price</th>
                                        <th class="text-right p-1 font-semibold text-gray-700 border-r border-gray-300 w-20">SP/MOQ</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-12">CGST%</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-12">UOM</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">Sales Org</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">Dist Channel</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-12">Plant</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 w-16">Commodity Code</th>
                                    </tr>
                                </thead>
                                <tbody id="search-results-tbody" class="bg-white">
                                    <!-- Results will be populated here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="bg-gray-50 px-4 py-3 flex items-center justify-between border-t">
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-gray-700">Show</span>
                                <select id="results-per-page" class="border border-gray-300 rounded px-2 py-1 text-sm">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="text-sm text-gray-700">entries</span>
                            </div>

                            <div class="flex items-center gap-2">
                                <button id="prev-page" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50" disabled>Previous</button>
                                <span id="page-info" class="text-sm text-gray-700">Page 1 of 1</span>
                                <button id="next-page" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50" disabled>Next</button>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="bg-gray-100 p-4 flex flex-wrap gap-3">
                            <button id="proceed-btn" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 flex items-center gap-2" disabled aria-label="Proceed with selected items">
                                <i data-lucide="arrow-right" class="w-4 h-4" aria-hidden="true"></i>
                                <span>Proceed</span>
                            </button>
                            <button id="add-to-cart-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 flex items-center gap-2" disabled aria-label="Add selected items to cart">
                                <i data-lucide="shopping-cart" class="w-4 h-4" aria-hidden="true"></i>
                                <span>Add To Cart</span>
                            </button>
                            <button id="clear-selection-btn" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors font-medium flex items-center gap-2" aria-label="Clear all selections">
                                <i data-lucide="x-circle" class="w-4 h-4" aria-hidden="true"></i>
                                <span>Clear Selection</span>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white rounded-2xl shadow-xl p-6 card-hover">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">Recent Activity</h3>
                            <button class="text-sm text-blue-600 hover:text-blue-800 font-medium focus-ring rounded-lg px-3 py-1">View All</button>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center gap-4 p-3 bg-gray-50 rounded-xl">
                                <div class="p-2 bg-green-100 rounded-lg">
                                    <i data-lucide="check-circle" class="w-4 h-4 text-green-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">Order #ORD-2024-156 completed</p>
                                    <p class="text-xs text-gray-500">Drive belt assembly delivered</p>
                                </div>
                                <span class="text-xs text-gray-400">2 hours ago</span>
                            </div>
                            <div class="flex items-center gap-4 p-3 bg-gray-50 rounded-xl">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <i data-lucide="truck" class="w-4 h-4 text-blue-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">Order #ORD-2024-155 shipped</p>
                                    <p class="text-xs text-gray-500">Bearing set for Model B</p>
                                </div>
                                <span class="text-xs text-gray-400">5 hours ago</span>
                            </div>
                            <div class="flex items-center gap-4 p-3 bg-gray-50 rounded-xl">
                                <div class="p-2 bg-yellow-100 rounded-lg">
                                    <i data-lucide="package-plus" class="w-4 h-4 text-yellow-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">New order #ORD-2024-157 created</p>
                                    <p class="text-xs text-gray-500">Hydraulic pump components</p>
                                </div>
                                <span class="text-xs text-gray-400">1 day ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Floating Action Buttons -->
    <div class="fixed bottom-6 right-6 flex flex-col gap-3 z-40" id="floating-actions">
        <!-- Main FAB -->
        <button id="main-fab" class="w-14 h-14 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group" onclick="toggleFloatingMenu()" aria-label="Quick actions menu">
            <i data-lucide="plus" class="w-6 h-6 transition-transform duration-300 group-hover:rotate-45" aria-hidden="true"></i>
        </button>

        <!-- Secondary FABs -->
        <div id="fab-menu" class="flex flex-col gap-3 opacity-0 scale-0 transition-all duration-300 origin-bottom">
            <button class="w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="floatingAction('search')" aria-label="Quick search">
                <i data-lucide="search" class="w-5 h-5" aria-hidden="true"></i>
            </button>
            <button class="w-12 h-12 bg-green-600 hover:bg-green-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="floatingAction('newOrder')" aria-label="New order">
                <i data-lucide="plus-circle" class="w-5 h-5" aria-hidden="true"></i>
            </button>
            <button class="w-12 h-12 bg-orange-600 hover:bg-orange-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="floatingAction('track')" aria-label="Track order">
                <i data-lucide="truck" class="w-5 h-5" aria-hidden="true"></i>
            </button>
            <button class="w-12 h-12 bg-purple-600 hover:bg-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="floatingAction('ai')" aria-label="AI assistant">
                <i data-lucide="brain" class="w-5 h-5" aria-hidden="true"></i>
            </button>
        </div>
    </div>

    <!-- Activity Log Modal -->
    <div id="activity-log-modal" class="fixed inset-0 z-50 hidden" role="dialog" aria-modal="true" aria-labelledby="activity-log-title">
        <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300" onclick="closeActivityLog()"></div>
        <div class="relative flex items-center justify-center min-h-screen p-4">
            <div class="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl transform transition-all duration-300 overflow-hidden">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-blue-50">
                    <div class="flex items-center gap-3">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i data-lucide="activity" class="w-6 h-6 text-blue-600" aria-hidden="true"></i>
                        </div>
                        <div>
                            <h3 id="activity-log-title" class="text-xl font-semibold text-gray-900">Activity Log</h3>
                            <p class="text-sm text-gray-600">Complete history of user actions</p>
                        </div>
                    </div>
                    <button onclick="closeActivityLog()" class="p-2 text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors focus-ring" aria-label="Close activity log">
                        <i data-lucide="x" class="w-5 h-5" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div id="activity-log-content" class="space-y-4">
                        <!-- Activity items will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Results Modal -->
    <div id="ai-modal" class="fixed inset-0 z-50 hidden" role="dialog" aria-modal="true" aria-labelledby="ai-modal-title">
        <div id="ai-modal-backdrop" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300"></div>
        <div class="relative flex items-center justify-center min-h-screen p-4">
            <div id="ai-modal-panel" class="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl transform transition-all duration-300 opacity-0 scale-95 overflow-hidden">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-yellow-50">
                    <div class="flex items-center gap-3">
                        <div class="p-3 bg-yellow-100 rounded-lg">
                            <i data-lucide="brain" class="w-6 h-6 text-yellow-600" aria-hidden="true"></i>
                        </div>
                        <div>
                            <h3 id="ai-modal-title" class="text-xl font-semibold text-gray-900">AI Assistant</h3>
                            <p class="text-sm text-gray-600">Intelligent part analysis and recommendations</p>
                        </div>
                    </div>
                    <button id="close-ai-modal" class="p-2 text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors focus-ring" aria-label="Close AI Assistant modal">
                        <i data-lucide="x" class="w-5 h-5" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <!-- Loading State -->
                    <div id="ai-modal-loading" class="text-center py-16 hidden">
                        <div class="spinner w-16 h-16 border-4 border-yellow-500 border-t-transparent rounded-full mx-auto mb-6"></div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">AI is analyzing...</h4>
                        <p class="text-gray-600">Please wait while we process your request</p>
                        <div class="mt-4 flex justify-center">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full animate-bounce"></div>
                                <div class="w-2 h-2 bg-yellow-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                <div class="w-2 h-2 bg-yellow-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div id="ai-modal-content" class="prose prose-lg max-w-none"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // --- Global Variables ---
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const navTexts = document.querySelectorAll('.nav-text');
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileCloseBtn = document.getElementById('mobile-close-btn');
        const mobileOverlay = document.getElementById('mobile-overlay');
        const toastContainer = document.getElementById('toast-container');

        // --- Utility Functions ---
        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            const bgColor = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            }[type] || 'bg-blue-500';

            toast.className = `toast ${bgColor} text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-3 mb-2`;
            toast.innerHTML = `
                <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : type === 'warning' ? 'alert-triangle' : 'info'}" class="w-5 h-5"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()" class="ml-auto hover:bg-white hover:bg-opacity-20 rounded p-1">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            `;

            toastContainer.appendChild(toast);
            lucide.createIcons();

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Auto remove
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // --- Mobile Navigation Logic ---
        function openMobileMenu() {
            sidebar.classList.remove('-translate-x-full');
            mobileOverlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeMobileMenu() {
            sidebar.classList.add('-translate-x-full');
            mobileOverlay.classList.add('hidden');
            document.body.style.overflow = '';
        }

        // --- Desktop Sidebar Logic (Hover-based) ---
        const expandSidebar = () => {
            if (window.innerWidth >= 1024) { // Only on desktop
                sidebar.classList.remove('lg:w-20');
                sidebar.classList.add('lg:w-64');
                mainContent.classList.remove('lg:ml-20');
                mainContent.classList.add('lg:ml-64');
                navTexts.forEach(text => {
                    text.classList.remove('opacity-0');
                });
            }
        };

        const collapseSidebar = () => {
            if (window.innerWidth >= 1024) { // Only on desktop
                sidebar.classList.add('lg:w-20');
                sidebar.classList.remove('lg:w-64');
                mainContent.classList.add('lg:ml-20');
                mainContent.classList.remove('lg:ml-64');
                navTexts.forEach(text => {
                    text.classList.add('opacity-0');
                });
            }
        };

        // --- Event Listeners ---
        mobileMenuBtn?.addEventListener('click', openMobileMenu);
        mobileCloseBtn?.addEventListener('click', closeMobileMenu);
        mobileOverlay?.addEventListener('click', closeMobileMenu);

        // Desktop sidebar hover
        sidebar.addEventListener('mouseenter', expandSidebar);
        sidebar.addEventListener('mouseleave', collapseSidebar);

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1024) {
                closeMobileMenu();
            }
        });

        // Update time every minute
        updateCurrentTime();
        setInterval(updateCurrentTime, 60000);

        // --- Activity Logging System ---
        let activityLog = JSON.parse(localStorage.getItem('userActivityLog') || '[]');

        function logActivity(action, details = {}) {
            const activity = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                action: action,
                details: details,
                user: 'Kanpur Plastipack Limited'
            };

            activityLog.unshift(activity);

            // Keep only last 100 activities
            if (activityLog.length > 100) {
                activityLog = activityLog.slice(0, 100);
            }

            localStorage.setItem('userActivityLog', JSON.stringify(activityLog));
            updateRecentActivity();
        }

        function updateRecentActivity() {
            // Update recent activity section with latest logs
            const recentActivities = activityLog.slice(0, 3);
            // This would update the recent activity section in the UI
        }

        // --- Floating Action Buttons ---
        let fabMenuOpen = false;

        function toggleFloatingMenu() {
            const fabMenu = document.getElementById('fab-menu');
            const mainFab = document.getElementById('main-fab');

            fabMenuOpen = !fabMenuOpen;

            if (fabMenuOpen) {
                fabMenu.classList.remove('opacity-0', 'scale-0');
                fabMenu.classList.add('opacity-100', 'scale-100');
                mainFab.querySelector('i').style.transform = 'rotate(45deg)';
            } else {
                fabMenu.classList.add('opacity-0', 'scale-0');
                fabMenu.classList.remove('opacity-100', 'scale-100');
                mainFab.querySelector('i').style.transform = 'rotate(0deg)';
            }

            logActivity('fab_menu_toggle', { opened: fabMenuOpen });
        }

        function floatingAction(action) {
            logActivity('floating_action', { action: action });

            switch(action) {
                case 'search':
                    switchTab('numberEnquiry');
                    document.getElementById('catalogNumber')?.focus();
                    showToast('Quick search activated', 'info');
                    break;
                case 'newOrder':
                    showToast('Redirecting to new order...', 'info');
                    break;
                case 'track':
                    showToast('Opening order tracking...', 'info');
                    break;
                case 'ai':
                    switchTab('aiSearch');
                    showToast('AI Assistant activated', 'info');
                    break;
            }

            toggleFloatingMenu(); // Close menu after action
        }

        // --- Quick Actions Functions ---
        function quickAction(action) {
            logActivity('quick_action', { action: action });

            switch(action) {
                case 'search':
                    switchTab('numberEnquiry');
                    document.getElementById('catalogNumber')?.focus();
                    break;
                case 'newOrder':
                    showToast('Creating new order...', 'info');
                    break;
                case 'trackOrder':
                    showToast('Opening order tracking...', 'info');
                    break;
                case 'aiAssist':
                    switchTab('aiSearch');
                    break;
            }
        }

        function customizeQuickActions() {
            logActivity('customize_quick_actions');
            showToast('Quick Actions customization coming soon!', 'info');
        }

        // --- Today's Tasks Functions ---
        function completeTask(checkbox, taskId) {
            const taskItem = checkbox.closest('.task-item');

            if (checkbox.checked) {
                taskItem.style.opacity = '0.6';
                taskItem.style.textDecoration = 'line-through';
                logActivity('task_completed', { taskId: taskId });
                showToast('Task completed!', 'success');
            } else {
                taskItem.style.opacity = '1';
                taskItem.style.textDecoration = 'none';
                logActivity('task_uncompleted', { taskId: taskId });
            }
        }

        function viewAllTasks() {
            logActivity('view_all_tasks');
            showToast('Opening full task list...', 'info');
        }

        // --- Navigation Functions ---
        function navigateToSection(section) {
            logActivity('navigate_to_section', { section: section });
            showToast(`Navigating to ${section}...`, 'info');
        }

        // --- Activity Log Modal ---
        function showActivityLog() {
            const modal = document.getElementById('activity-log-modal');
            const content = document.getElementById('activity-log-content');

            // Populate activity log
            content.innerHTML = activityLog.map(activity => `
                <div class="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div class="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                        <i data-lucide="activity" class="w-4 h-4 text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">${activity.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                        <p class="text-xs text-gray-600">${new Date(activity.timestamp).toLocaleString()}</p>
                        ${activity.details && Object.keys(activity.details).length > 0 ?
                            `<p class="text-xs text-gray-500 mt-1">${JSON.stringify(activity.details)}</p>` : ''}
                    </div>
                </div>
            `).join('');

            modal.classList.remove('hidden');
            logActivity('view_activity_log');
        }

        function closeActivityLog() {
            document.getElementById('activity-log-modal').classList.add('hidden');
        }

        // Log initial page load
        logActivity('page_load', { page: 'home' });


        // --- Enhanced Tab Switching Logic ---
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        function switchTab(targetTabId) {
            tabButtons.forEach(btn => {
                const isActive = btn.dataset.tab === targetTabId;
                btn.classList.toggle('text-yellow-600', isActive);
                btn.classList.toggle('border-yellow-500', isActive);
                btn.classList.toggle('text-gray-500', !isActive);
                btn.classList.toggle('hover:text-gray-700', !isActive);
                btn.setAttribute('aria-selected', isActive);
            });

            tabContents.forEach(content => {
                const isVisible = content.id === targetTabId;
                content.classList.toggle('hidden', !isVisible);
                if (isVisible) {
                    content.style.animation = 'fadeIn 0.3s ease-in-out';
                }
            });

            // Update breadcrumb
            updateBreadcrumb(targetTabId);

            showToast(`Switched to ${targetTabId.replace(/([A-Z])/g, ' $1').toLowerCase()}`, 'info', 2000);
        }

        function updateBreadcrumb(tabId) {
            const breadcrumbMap = {
                'numberEnquiry': 'Parts Number Enquiry',
                'priceEnquiry': 'Price Enquiry',
                'aiSearch': 'AI-Powered Search'
            };

            const currentPageElement = document.getElementById('current-page');
            if (currentPageElement && breadcrumbMap[tabId]) {
                currentPageElement.textContent = breadcrumbMap[tabId];
            }
        }

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                switchTab(button.dataset.tab);
            });

            // Keyboard navigation
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    switchTab(button.dataset.tab);
                }
            });
        });

        // --- Enhanced AI Modal Logic ---
        const modal = document.getElementById('ai-modal');
        const modalBackdrop = document.getElementById('ai-modal-backdrop');
        const modalPanel = document.getElementById('ai-modal-panel');
        const closeModalButton = document.getElementById('close-ai-modal');
        const modalTitle = document.getElementById('ai-modal-title');
        const modalLoading = document.getElementById('ai-modal-loading');
        const modalContent = document.getElementById('ai-modal-content');

        function showModal() {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            setTimeout(() => {
                modalBackdrop.classList.remove('opacity-0');
                modalPanel.classList.remove('opacity-0', 'scale-95');
            }, 10);
        }

        function hideModal() {
            modalBackdrop.classList.add('opacity-0');
            modalPanel.classList.add('opacity-0', 'scale-95');
            document.body.style.overflow = '';

            setTimeout(() => modal.classList.add('hidden'), 300);
        }

        function setModalLoading(isLoading) {
            modalLoading.classList.toggle('hidden', !isLoading);
            modalContent.classList.toggle('hidden', isLoading);
        }

        // Enhanced modal event listeners
        closeModalButton.addEventListener('click', hideModal);
        modalBackdrop.addEventListener('click', hideModal);

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                hideModal();
            }
        });

        // --- Form Enhancement Functions ---
        function validateForm(formElement) {
            const inputs = formElement.querySelectorAll('input[required], textarea[required], select[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('border-red-500');
                    isValid = false;
                } else {
                    input.classList.remove('border-red-500');
                }
            });

            return isValid;
        }

        function addFormLoadingState(button) {
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = `
                <div class="flex items-center justify-center gap-2">
                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Processing...</span>
                </div>
            `;

            return () => {
                button.disabled = false;
                button.innerHTML = originalText;
                lucide.createIcons();
            };
        }

        // --- Enhanced API Call Function ---
        async function callGeminiAPI(prompt, isJson = false) {
            const apiKey = ""; // Leave blank for demo

            // Simulate API call for demo purposes
            if (!apiKey) {
                await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate delay

                if (isJson) {
                    return JSON.stringify([
                        { partNumber: "LP-2024-001", reason: "High-performance drive belt suitable for heavy-duty applications" },
                        { partNumber: "LP-2024-002", reason: "Reinforced belt with enhanced durability for continuous operation" },
                        { partNumber: "LP-2024-003", reason: "Standard belt for general purpose weaving machines" }
                    ]);
                } else {
                    return `
# Part Analysis Report

## Overview
This appears to be an industrial machine part commonly used in textile manufacturing equipment.

## Key Features
- **Material**: High-grade steel alloy
- **Application**: Primary drive systems
- **Compatibility**: Model X series weavers
- **Durability**: Rated for 10,000+ operating hours

## Maintenance Tips
- Regular lubrication every 500 hours
- Visual inspection for wear patterns
- Replace when tolerance exceeds 0.1mm

## Installation Notes
- Requires specialized tools
- Follow torque specifications: 45-50 Nm
- Ensure proper alignment before operation
                    `;
                }
            }

            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            let payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

            if (isJson) {
                payload.generationConfig = {
                    responseMimeType: "application/json",
                    responseSchema: {
                        type: "ARRAY",
                        items: {
                            type: "OBJECT",
                            properties: {
                                partNumber: { "type": "STRING" },
                                reason: { "type": "STRING" }
                            },
                            required: ["partNumber", "reason"]
                        }
                    }
                };
            }

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    throw new Error(`API call failed with status: ${response.status}`);
                }

                const result = await response.json();

                if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts[0]) {
                    return result.candidates[0].content.parts[0].text;
                } else {
                    console.error("Unexpected API response structure:", result);
                    throw new Error("Invalid response structure from AI service");
                }
            } catch (error) {
                console.error("Error calling Gemini API:", error);
                showToast(`AI service error: ${error.message}`, 'error');
                throw error;
            }
        }

        // --- Enhanced Form Handlers ---

        // Price Enquiry Form
        const priceEnquiryForm = document.getElementById('priceEnquiryForm');
        priceEnquiryForm?.addEventListener('submit', async (e) => {
            e.preventDefault();
            const partNumber = document.getElementById('partNumber').value.trim();

            if (!partNumber) {
                showToast('Please enter a part number', 'warning');
                return;
            }

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const resetLoading = addFormLoadingState(submitBtn);

            try {
                // Simulate price lookup
                await new Promise(resolve => setTimeout(resolve, 1500));

                const mockPrice = (Math.random() * 500 + 50).toFixed(2);
                const availability = Math.random() > 0.3 ? 'In Stock' : 'Limited Stock';

                showToast(`Part ${partNumber}: $${mockPrice} - ${availability}`, 'success', 5000);
            } catch (error) {
                showToast('Failed to fetch price information', 'error');
            } finally {
                resetLoading();
            }
        });

        // AI Analysis Feature
        const getAiAnalysisBtn = document.getElementById('getAiAnalysis');
        getAiAnalysisBtn?.addEventListener('click', async () => {
            const partNumber = document.getElementById('partNumber').value.trim();

            if (!partNumber) {
                showToast('Please enter a Part Number first', 'warning');
                return;
            }

            const resetLoading = addFormLoadingState(getAiAnalysisBtn);
            modalTitle.innerText = `🔍 AI Analysis for Part #${partNumber}`;
            showModal();
            setModalLoading(true);

            try {
                const prompt = `Provide a detailed analysis for an industrial machine part with the number '${partNumber}'. Include its likely function, common applications, typical materials, and potential maintenance or installation tips. Format the response in clear, well-structured markdown.`;
                const result = await callGeminiAPI(prompt);

                // Enhanced markdown to HTML conversion
                let htmlResult = result
                    .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold text-gray-900 mb-3 mt-6">$1</h3>')
                    .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold text-gray-900 mb-4 mt-8">$1</h2>')
                    .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-gray-900 mb-6">$1</h1>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em class="italic text-gray-700">$1</em>')
                    .replace(/`(.*?)`/g, '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">$1</code>')
                    .replace(/^- (.*$)/gim, '<li class="ml-4 mb-1">$1</li>')
                    .replace(/\n\n/g, '</p><p class="mb-4">')
                    .replace(/\n/g, '<br>');

                modalContent.innerHTML = `<div class="prose prose-lg max-w-none"><p class="mb-4">${htmlResult}</p></div>`;
                showToast('AI analysis completed', 'success');
            } catch (error) {
                modalContent.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-500 mb-4">
                            <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-2"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Analysis Failed</h3>
                        <p class="text-gray-600">Unable to analyze part ${partNumber}. Please try again later.</p>
                    </div>
                `;
                lucide.createIcons();
            } finally {
                setModalLoading(false);
                resetLoading();
            }
        });

        // AI-Powered Part Search
        const aiSearchForm = document.getElementById('aiSearchForm');
        aiSearchForm?.addEventListener('submit', async (e) => {
            e.preventDefault();
            const description = document.getElementById('partDescription').value.trim();

            if (!description) {
                showToast('Please describe the part you are looking for', 'warning');
                return;
            }

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const resetLoading = addFormLoadingState(submitBtn);

            modalTitle.innerText = '✨ AI Part Number Suggestions';
            showModal();
            setModalLoading(true);

            try {
                const prompt = `Based on the description "${description}", suggest possible industrial machine part numbers. Provide a list of potential matches with a brief reason for each suggestion.`;
                const jsonString = await callGeminiAPI(prompt, true);
                const parts = JSON.parse(jsonString);

                let contentHtml = `
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">🎯 Suggested Parts</h3>
                        <p class="text-gray-600">Based on your description: "${description}"</p>
                    </div>
                    <div class="space-y-4">
                `;

                parts.forEach((part, index) => {
                    contentHtml += `
                        <div class="p-4 bg-blue-50 rounded-xl border border-blue-200 hover:shadow-md transition-shadow">
                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                                    ${index + 1}
                                </div>
                                <div class="flex-1">
                                    <p class="font-semibold text-gray-900 mb-1">
                                        Part Number: <code class="bg-white text-blue-600 px-3 py-1 rounded-lg text-sm font-mono">${part.partNumber}</code>
                                    </p>
                                    <p class="text-sm text-gray-700"><strong>Match Reason:</strong> ${part.reason}</p>
                                    <div class="mt-2 flex gap-2">
                                        <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors">
                                            Check Price
                                        </button>
                                        <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded-full hover:bg-gray-600 transition-colors">
                                            View Details
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                contentHtml += '</div>';
                modalContent.innerHTML = contentHtml;
                showToast('AI search completed successfully', 'success');
            } catch (error) {
                modalContent.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-yellow-500 mb-4">
                            <i data-lucide="search-x" class="w-12 h-12 mx-auto mb-2"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No Results Found</h3>
                        <p class="text-gray-600 mb-4">The AI couldn't find matching parts for your description.</p>
                        <p class="text-sm text-gray-500">Try being more specific about the part's function, size, or application.</p>
                    </div>
                `;
                lucide.createIcons();
            } finally {
                setModalLoading(false);
                resetLoading();
            }
        });

        // --- Search Results Grid Functions ---
        const searchResultsContainer = document.getElementById('search-results-container');
        const searchResultsTbody = document.getElementById('search-results-tbody');
        const resultsCount = document.getElementById('results-count');
        const proceedBtn = document.getElementById('proceed-btn');
        const addToCartBtn = document.getElementById('add-to-cart-btn');
        const clearSelectionBtn = document.getElementById('clear-selection-btn');

        // Sample search results data
        const sampleSearchResults = [
            {
                partNumber: '1117300009',
                description: 'Love Joy Coupling Type 1095',
                hsn: '8477',
                moq: 1,
                lotSize: 1,
                unitPrice: 'INR 255.00',
                spMoq: '0.00',
                cgst: '18',
                uom: 'EA',
                salesOrg: '1000',
                distChannel: 'AA',
                plant: '',
                commodityCode: '' 
            },
            {
                partNumber: '1117300010',
                description: 'Oil Seal Bolt 27',
                hsn: '7318',
                moq: 1,
                lotSize: 1,
                unitPrice: 'INR 50.00',
                spMoq: '0.00',
                cgst: '18',
                uom: 'EA',
                salesOrg: '1000',
                distChannel: 'AA',
                plant: '',
                commodityCode: ''
            },
            {
                partNumber: '1117300020',
                description: 'Barrel Inlet Jet, 350 x 7 Dia (D 75, 2016',
                hsn: '8488',
                moq: 1,
                lotSize: 1,
                unitPrice: 'INR 14,850.00',
                spMoq: '14850.00',
                cgst: '18',
                uom: 'EA',
                salesOrg: '1000',
                distChannel: 'AA',
                plant: '',
                commodityCode: ''
            },
            {
                partNumber: '1117300030',
                description: 'Cam Shaft Compl 240V 1 Inch (D 75, 2016',
                hsn: '8469',
                moq: 1,
                lotSize: 1,
                unitPrice: 'INR 1,380.00',
                spMoq: '0.00',
                cgst: '18',
                uom: 'EA',
                salesOrg: '1000',
                distChannel: 'AA',
                plant: '',
                commodityCode: ''
            }
        ];

        function displaySearchResults(results) {
            searchResultsTbody.innerHTML = '';
            resultsCount.textContent = results.length;

            results.forEach((result, index) => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors border-b border-gray-200';
                row.innerHTML = `
                    <td class="p-1 text-center border-r border-gray-300">
                        <input type="checkbox" class="result-checkbox w-3 h-3 text-yellow-500 border-gray-300 rounded focus:ring-yellow-500"
                               data-index="${index}" onchange="updateActionButtons()">
                    </td>
                    <td class="p-1 font-medium text-blue-600 border-r border-gray-300 truncate text-xs" title="${result.partNumber}">${result.partNumber}</td>
                    <td class="p-1 border-r border-gray-300 truncate text-xs" title="${result.description}">${result.description}</td>
                    <td class="p-1 text-center border-r border-gray-300 text-xs">${result.hsn}</td>
                    <td class="p-1 text-center border-r border-gray-300 text-xs">${result.moq}</td>
                    <td class="p-1 text-center border-r border-gray-300 text-xs">${result.lotSize}</td>
                    <td class="p-1 text-right font-semibold border-r border-gray-300 text-xs whitespace-nowrap">${result.unitPrice}</td>
                    <td class="p-1 text-right border-r border-gray-300 text-xs">${result.spMoq}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.cgst}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.uom}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.salesOrg}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.distChannel}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.plant}</td>
                    <td class="p-1 text-center">${result.commodityCode}</td>
                `;
                searchResultsTbody.appendChild(row);
            });

            searchResultsContainer.classList.remove('hidden');
            searchResultsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            updateActionButtons();
        }

        function updateActionButtons() {
            const checkedBoxes = document.querySelectorAll('.result-checkbox:checked');
            const hasSelection = checkedBoxes.length > 0;

            proceedBtn.disabled = !hasSelection;
            addToCartBtn.disabled = !hasSelection;

            if (hasSelection) {
                proceedBtn.classList.remove('opacity-50');
                addToCartBtn.classList.remove('opacity-50');
            } else {
                proceedBtn.classList.add('opacity-50');
                addToCartBtn.classList.add('opacity-50');
            }
        }

        function performSearch(searchType, searchValue) {
            // Simulate search delay
            setTimeout(() => {
                // Filter results based on search value (simple simulation)
                let filteredResults = sampleSearchResults;
                if (searchValue && searchValue.trim()) {
                    filteredResults = sampleSearchResults.filter(result =>
                        result.partNumber.toLowerCase().includes(searchValue.toLowerCase()) ||
                        result.description.toLowerCase().includes(searchValue.toLowerCase())
                    );
                }

                displaySearchResults(filteredResults);
                showToast(`Found ${filteredResults.length} matching parts`, 'success');
            }, 500);
        }

        // Action button handlers
        proceedBtn?.addEventListener('click', () => {
            const selectedItems = document.querySelectorAll('.result-checkbox:checked');
            showToast(`Proceeding with ${selectedItems.length} selected items`, 'info');
        });

        addToCartBtn?.addEventListener('click', () => {
            const selectedItems = document.querySelectorAll('.result-checkbox:checked');
            showToast(`Added ${selectedItems.length} items to cart`, 'success');
        });

        clearSelectionBtn?.addEventListener('click', () => {
            document.querySelectorAll('.result-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            updateActionButtons();
            showToast('Selection cleared', 'info');
        });

        // Search button event listeners
        document.getElementById('catalog-search-btn')?.addEventListener('click', () => {
            const catalogNumber = document.getElementById('catalogNumber').value.trim();
            if (!catalogNumber) {
                showToast('Please enter a catalog number', 'warning');
                return;
            }
            showToast('Searching for parts...', 'info', 2000);
            performSearch('catalog', catalogNumber);
        });

        document.getElementById('serial-search-btn')?.addEventListener('click', () => {
            const mfgYear = document.getElementById('mfgYear').value;
            const modelDesc = document.getElementById('modelDesc').value;
            const serialNumber = document.getElementById('serialNumber').value;

            if (!mfgYear || !modelDesc) {
                showToast('Please select manufacturing year and model description', 'warning');
                return;
            }

            showToast('Searching for parts...', 'info', 2000);
            performSearch('serial', `${mfgYear}-${modelDesc}-${serialNumber}`);
        });

        // Add Enter key support for catalog search
        document.getElementById('catalogNumber')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('catalog-search-btn').click();
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            showToast('Welcome to Lohia Order Management System', 'success', 4000);

            // Add form validation on input
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    if (input.hasAttribute('required') && !input.value.trim()) {
                        input.classList.add('border-red-300');
                    } else {
                        input.classList.remove('border-red-300');
                    }
                });
            });
        });
    </script>
</body>
</html>
